#%%#!/usr/bin/env python

import yaml
from pathlib import Path
from tqdm import tqdm

import torch
import numpy as np
import matplotlib.pyplot as plt

from DataYatesV1 import (
    DictDataset, enable_autoreload, get_gaborium_sta_ste, get_session, ensure_tensor,
    get_complete_sessions, print_batch, set_seeds, calc_sta
)

from scipy.ndimage import gaussian_filter

set_seeds(1002)

# Enable autoreload for interactive development
enable_autoreload()
device = 'cuda:1'

base_config_path = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis/mult_data_stimembed_base.yaml")
with open(base_config_path, 'r') as f:
    base_config = yaml.safe_load(f)

#%% Utility Functions
def get_quality_metrics(sess, n_lags=24):
    
    # -------------------------------------------------------------------------------------------
    # Visual Responsiveness Metric
    # -------------------------------------------------------------------------------------------
    sta_data, ste_data = get_gaborium_sta_ste(sess, n_lags)

    signal = np.abs(ste_data - np.median(ste_data, axis=(2,3), keepdims=True))
    sigma = [0, 2, 2, 2]
    signal = gaussian_filter(signal, sigma)
    noise = np.median(signal[:,0], axis=(1,2))
    snr_per_lag = np.max(signal, axis=(2,3)) / noise[:,None]
    cluster_lag = snr_per_lag.argmax(axis=1)

    from DataYatesV1 import plot_stas
    plot_stas(signal[:,:,None,:,:])

    visual_snr = snr_per_lag.max(axis=1)

    # -------------------------------------------------------------------------------------------
    # Spike sorting quality metrics
    # -------------------------------------------------------------------------------------------


    # extract missing % and contamination %
    spike_clusters = sess.ks_results.spike_clusters
    spike_amplitudes = sess.ks_results.spike_amplitudes
    cids = np.unique(spike_clusters)

    refractory = np.load(sess.sess_dir / 'qc' / 'refractory' / 'refractory.npz')
    refractory_periods = refractory['refractory_periods']
    min_contam_proportions = refractory['min_contam_props']
    contam_pct = np.array([np.min(min_contam_proportions[iU])*100 for iU in range(len(cids))])

    truncation = np.load(sess.sess_dir / 'qc' / 'amp_truncation' / 'truncation.npz')
    med_missing_pct = np.array([np.median(truncation['mpcts'][truncation['cid']==iU]) for iU in range(len(cids))])

    return visual_snr, med_missing_pct, contam_pct

# %%
snr_threshold = 5
missing_threshold = 25
contamination_threshold = 50

sessions = get_complete_sessions()

sess = sessions[0]

visual_snr, med_missing_pct, contam_pct = get_quality_metrics(sess, n_lags=24)

visually_responsive = np.where(visual_snr > snr_threshold)[0]
print(f'{len(visually_responsive)} / {len(visual_snr)} units are visually responsive')

not_missing_spikes = np.where(med_missing_pct < missing_threshold)[0]
print(f'{len(not_missing_spikes)} / {len(med_missing_pct)} units are not missing spikes')

not_contaminated = np.where(contam_pct < contamination_threshold)[0]
print(f'{len(not_contaminated)} / {len(contam_pct)} units are not contaminated')

good_units = np.intersect1d(visually_responsive, not_contaminated)

print(f'{len(good_units)} / {len(visual_snr)} units are responsive and pass contamination rate criteria')


# %%

