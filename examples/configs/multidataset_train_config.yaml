# Multidataset training configuration
# This config specifies the model and multiple datasets for training

training_type: v1multi

# Path to the main model configuration (shared components)
model_config: "test_model_multidataset.yaml"

# List of dataset configurations
datasets:
  - path: "test_data_stimcones_symlog_5lags.yaml"
    weight: 1.0  # Optional: dataset sampling weight (default: 1.0)
  - path: "test_data_stimcones_symlog_5lags_dataset2.yaml"
    weight: 1.0

# Training-specific parameters
training:
  accumulate_grad_batches: 1
  max_epochs: 100
  batch_size: 32
  early_stopping_patience: 10
  
# Optimizer configuration
optimizer:
  type: AdamW
  lr: 5e-4
  weight_decay: 1e-5

# Scheduler configuration (optional)
scheduler:
  type: cosine
  warmup_epochs: 5
