# V1 multidataset model configuration
# This model uses adapter frontends (per dataset) -> shared DenseNet -> shared recurrent -> readouts (per dataset)
model_type: v1multi

# Model dimensions
height: 32
width: 32
sampling_rate: 240

# Note: Frontend configurations are specified in individual dataset configs
# All frontends must be of type 'adapter' for multidataset training

# Shared convnet configuration
convnet:
  type: densenet
  params:
    growth_rate: 8
    num_blocks: 3
    dim: 3
    checkpointing: true
    block_config:
      conv_params:
        type: depthwise
        dim: 3
        kernel_size: [3, 5, 5]
        padding: [1, 2, 2]
      norm_type: rms
      act_type: mish
      pool_params: {}

# Shared modulator configuration
modulator:
  type: film
  params:
    behavior_dim: 40
    feature_dim: 32  # Number of convnet output channels
    encoder_params:
      type: mlp
      dims: [128, 128]  # Hidden layers + output size
      activation: gelu
      bias: true
      residual: true
      dropout: 0.1
      last_layer_activation: true

# Shared recurrent configuration
recurrent:
  type: none
  params: {}

# Note: Readout configurations are specified in individual dataset configs
# Each dataset will have its own readout with n_units = len(cids)
